---
description: 
globs: 
alwaysApply: false
---
# 学习通自动化系统 - 速度控制规则

## 答题速度控制

### 作业答题速度
- **题目间隔**: 每道题处理后等待0.5秒 (`time.sleep(0.5)`) - 位于`API/WorkTask.py`第131行
- **提交后等待**: 作业提交后等待0.5秒 (`time.sleep(0.5)`) - 位于`API/WorkTask.py`第134行
- **答案匹配策略**: 使用不同的匹配阈值(0.95, 0.9, 0.7, 0.65)逐步降低匹配精度以找到答案

### 考试答题速度
- **题目间隔**: 非最后一题等待0.05秒，最后一题等待0.5秒 - 位于`data/Exam.py`第230行
- **考试完成后等待**: 考试完成后随机等待10-15秒 (`time.sleep(random.randint(10,15))`) - 位于`data/Exam.py`第93行
- **验证码失败重试**: 验证码识别失败后随机等待1-2秒 (`time.sleep(random.randint(1,2))`) - 位于`data/Exam.py`第143行
- **最终提交等待**: 注释掉的代码显示原本设计为最后一题提交前等待600-900秒(10-15分钟)

### 讨论任务速度
- **讨论任务完成后**: 无特定等待时间，由任务点处理的通用等待控制

### 视频任务速度
- **视频播放模拟**: 每0.5秒增加2秒播放时间 (`time.sleep(0.5)`) - 位于`API/VideoTask.py`第99行
- **视频上报间隔**: 每30秒上报一次播放进度 - 位于`API/VideoTask.py`第92行
- **视频完成后等待**: 视频任务完成后随机等待2-5秒 - 位于`学习通跑单启动文件.py`第217行

### 章节任务速度
- **章节间隔**: 每章节完成后随机等待60-90秒(1-1.5分钟) - 位于`学习通跑单启动文件.py`第220行
- **任务点间隔**: 每个任务点处理后等待0.5秒 - 位于`API/TaskDo.py`第129行

### 系统控制速度
- **订单获取间隔**: 每次获取订单列表后等待30秒 - 位于`学习通跑单启动文件.py`第322行
- **线程创建间隔**: 每个新订单线程创建后等待5秒 - 位于`学习通跑单启动文件.py`第307行
- **夜间检查间隔**: 夜间休息模式每15分钟检查一次时间 - 位于`学习通跑单启动文件.py`第266行

## 速度调整建议

### 答题速度优化
1. **作业答题加速**:
   - 已将`API/WorkTask.py`第131行和134行的`time.sleep(1)`值优化为0.5秒
   - 视需要可进一步缩短至0.3秒，但需注意反作弊机制

2. **考试答题加速**:
   - 已将考试答题等待时间优化为非最后一题0.05秒，最后一题0.5秒
   - 考试完成后的等待时间已从20-30秒缩短为10-15秒

3. **批量处理优化**:
   - 对于多题目的作业，可考虑根据题目总数动态调整等待时间

### 安全考虑
- 速度调整已考虑平台的反作弊机制
- 保留了一定的随机性，避免固定间隔
- 建议进行小范围测试，确保账号安全

