# -*- coding: utf-8 -*-
import threading
import datetime
from data.Exam import EXAM
from Config.UserSql import OrderProcessorsql
from API.TaskDo import Task
from data.Porgres import StartProces
from API.Session import StartSession
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from base64 import b64encode
from loguru import logger
from bs4 import BeautifulSoup
import random
import re
import string
import time
import requests
import parsel
import json
import traceback
def encrypt_by_aes(message):
    key = "u2oh6Vu^HWe4_AES"
    # 确保密钥长度为AES密钥长度要求（16, 24, 32）
    key = key.encode('utf-8')  # 将密钥转换为字节
    iv = key  # 在这个例子中，我们使用密钥作为初始化向量，但这不是最佳实践
    cipher = AES.new(key, AES.MODE_CBC, iv)
    padded_data = pad(message.encode('utf-8'), AES.block_size)
    encrypted_data = cipher.encrypt(padded_data)
    return b64encode(encrypted_data).decode('utf-8')
def t():
    return int(time.time() * 1000)

class UA:
    def __init__(self):
        self.WEB = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/117.0.2045.36'
        self.APP = {"User-Agent":f"/2.1.0 (Linux; U; Android 7.1.2; SM-G977N Build/LMY48Z) com.chaoxing.mobile/ChaoXingStudy_3_4.3.4_android_phone_494_27 (@Kalimdor)_{''.join(random.choice(string.hexdigits[:-6]) for _ in range(32))}"}





class MainXxt:
    def __init__(self,session,school,username,password,courseid,oid,cid,pool):
        self.session = session
        self.school = school
        self.pool = pool
        self.username = username
        self.password = password
        self.courseid = courseid
        self.oid = oid
        self.cid = cid
    def fanyalogin(self):
        pattern = r'^1[3456789]\d{9}$'
        # 使用 re.match() 函数进行匹配
        if re.match(pattern, self.username):
            res = self.session.post("https://passport2.chaoxing.com/fanyalogin",params={
                "fid": "-1",
                "uname": encrypt_by_aes(self.username),
                "password": encrypt_by_aes(self.password),
                "refer": "http%3A%2F%2Fi.mooc.chaoxing.com",
                "t": "true",
                "forbidotherlogin": "0",
                "validate": "",
                "doubleFactorLogin": "0",
                "independentId": "0",
                "independentNameId": "0"
            }).json()
            if res['status'] is True:
                return True
            else:
                return res['msg2']
        else:
            schoolcode = self.session.get(f"https://passport2.chaoxing.com/org/searchUnis?filter={self.school}").json()
            if schoolcode['result']:
                for i in schoolcode['froms']:
                    schoolid = i['schoolid']
                    api = f"https://passport2-api.chaoxing.com/v6/idNumberLogin?fid={schoolid}&idNumber={self.username}?pwd={self.password}&t=0"
                    res = self.session.post(api).json()
                    try:
                        sta = res['status']
                    except:
                        sta = res['result']
                    if sta is True:
                        return True
                    else:
                        pass
                return res['msg']
            else:
                return 'SchoolID获取失败 请检查是否为学校错误'
    def kclist(self):
        self.KcList = list()
        api = f"https://mooc1-api.chaoxing.com/mycourse/backclazzdata?view=json&mcode="
        try:
            logger.info(f"ID:{self.username},正在获取课程列表...")
            res = self.session.get(api, headers=UA().APP).json()
            logger.info(f"ID:{self.username},课程列表API返回: {res}")
            
            if 'channelList' not in res or not res['channelList']:
                logger.error(f"ID:{self.username},未找到课程列表或课程列表为空")
                return False
                
            for i in res['channelList']:
                try:
                    if 'content' not in i or 'course' not in i['content'] or 'data' not in i['content']['course'] or not i['content']['course']['data']:
                        continue
                        
                    kcid = int(i["content"]["course"]["data"][0]["id"])
                    logger.info(f"ID:{self.username},找到课程ID: {kcid}, 目标课程ID: {self.courseid}")
                    
                    if int(self.courseid) == kcid:
                        course_info = {
                            "kcname": i["content"]["course"]["data"][0]["name"], 
                            "courseid": self.courseid,
                            "clazzid": i["content"]["id"], 
                            "cpi": i["content"]["cpi"]
                        }
                        self.KcList.append(course_info)
                        logger.success(f"ID:{self.username},成功匹配课程: {course_info}")
                except Exception as e:
                    logger.error(f"ID:{self.username},处理课程信息时出错: {str(e)}")
                    continue
                    
            if self.KcList:
                return True
                
            logger.error(f"ID:{self.username},未找到匹配的课程ID: {self.courseid}")
            return False
        except Exception as e:
            logger.error(f"ID:{self.username},获取课程列表失败: {str(e)}")
            traceback.print_exc()
            return False

    def studentcourse(self):
        self.listid = list()
        self.kcname = self.KcList[0]['kcname']
        self.clazzid = self.KcList[0]['clazzid']
        self.cpi = self.KcList[0]['cpi']
        self.p = StartProces(self.session,self.KcList)
        r = BeautifulSoup(self.session.get("https://mooc2-ans.chaoxing.com/mooc2-ans/mycourse/studentcourse", params={
            "courseid": self.courseid,
            "clazzid": self.clazzid,
            "cpi": self.cpi,
            "ut": "s",
            "t": t()}).text, 'html.parser')
        if '很抱歉，您所浏览的页面不存在！' in r.text:
            self.listid.append('页面不存在')
            logger.success(f"ID:{self.username},未开课")
        else:
            for name in r.find_all("div", {"class": "chapter_unit"}):
                for i in name.find_all("li"):
                    status = i.find("span", {"class": "bntHoverTips"})
                    if status:status = status.text.strip()
                    else:status = None
                    if status is None:pass
                    else:
                        if status != '已完成':
                            title = i.find("a",{"class":"clicktitle"}).text.strip()
                            ListId = i.find("div", {"class": "chapter_item"}).get("id").replace("cur", "")
                            self.listid.append({"title":title,"id":ListId})
            logger.success(f"ID:{self.username},课件获取完成")
    def studentstudyAjax(self):
        self.cishu = None
        try:
            r = self.session.get(f"https://mooc1.chaoxing.com/mooc-ans/mycourse/studentstudyAjax", params={
                    "courseId": self.courseid,
                    "clazzid": self.clazzid,
                    "chapterId": self.chapterId,
                    "cpi": self.cpi,
                    "verificationcode": "false",
                    "mooc2": "1",
                    "microTopicId": "0"
                }).text
            res = BeautifulSoup(r, 'html.parser')
            self.noun = len(res.find("ul", {"class": "prev_ul clearfix"}).find_all("li"))
            try:self.cishu = parsel.Selector(r).xpath("//script[@type='text/javascript']/@src").get()
            except:pass
        except:
            self.noun = 2
    def studentstudy(self):
        if self.listid:
            value = 0
            for i in self.listid:
                title = i['title']
                self.sleep()
                if value % 2 == 0 or value == 0:
                    formatted_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    p, r = self.p.get_xxt_jxz()
                    r += f" | 实时执行：{title} | 更新:{formatted_time}"
                    self.pool.update_order(
                        f"update qingka_wangke_order set  process = '{p}',remarks = '{r}' where oid = '{self.oid}'")
                    logger.success(f"ID:{self.username},进度:{p},详情:{r}")
                value += 1
                self.chapterId = i['id']
                self.studentstudyAjax()
                for num in range(self.noun):
                    if self.cishu is not None:
                        self.session.get(self.cishu)  # 学习次数
                    r = self.session.get("https://mooc1.chaoxing.com/mooc-ans/knowledge/cards", params={
                        "clazzid": self.clazzid,
                        "courseid": self.courseid,
                        "knowledgeid": self.chapterId,
                        "num": num,
                        "ut": "s",
                        "cpi": self.cpi,
                        "v": "20160407-3",
                        "mooc2": "1",
                        "isMicroCourse": "false"
                    }).text
                    try:
                        html = str(BeautifulSoup(r, features="lxml").find("body").find_all("script")[0])
                        pattern = re.compile(r"mArg = ({[\s\S]*)}catch")
                        datas = re.findall(pattern, html)[0]
                        attachment = json.loads(datas.strip()[:-1])['attachments']
                        defaults = json.loads(datas.strip()[:-1])['defaults']
                        tk = Task(self.session,self.KcList,attachment,defaults,self.chapterId,self.username)
                        tk.task()
                        time.sleep(random.randint(2,5))
                    except:
                        pass
                time.sleep(random.randint(60,90))
            ks_s = self.p.get_xxt_wc()
            if ks_s is True:
                status = '已完成'
            else:
                em = EXAM(self.session, self.username, self.KcList, open=1)
                st = em.get_data()
                status = '待考试' if st is False else '已完成'
                if self.cid == 5385:
                    status = '已完成'
        else:
            if '页面不存在' in str(self.listid):
                status = '未开课'
            else:
                status = '已完成'
        formatted_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        p, r = self.p.get_xxt_jxz()
        r += f" | 实时执行：课程已完成【退出执行】 | 更新:{formatted_time}"
        self.pool.update_order(f"update qingka_wangke_order set status = '{status}',process='{p}',remarks='{r}' where oid = '{self.oid}'")

    def sleep(self):
        xun = 0
        shang = 0
        while True:
            current_time = datetime.datetime.now().time()
            # 晚上23点到第二天早上7点之间不执行任何操作，cid=5500不受此限制
            if (current_time >= datetime.time(23, 10) or current_time < datetime.time(7, 30)) and self.cid != 5500:
                xun += 1
                if xun == 1:
                    # logger.info(f"夜间休息")
                    formatted_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    p,r = self.p.get_xxt_jxz()
                    r += f" | 实时执行：人工进行已下线，等待次日7：30继续进行 | 更新:{formatted_time}"
                    try:
                        self.pool.update_order(
                            f"update qingka_wangke_order set status = '停止中',process='{p}',remarks='{r}' where oid = '{self.oid}'")
                    except:
                        self.pool.update_order(
                            f"update qingka_wangke_order set status = '停止中',process='{p}',remarks='{r}' where oid = '{self.oid}'")
                    shang += 1
                time.sleep(900)  # 每15分钟检查一次
                continue
            self.pool.update_order(
                f"update qingka_wangke_order set status = '进行中' where oid = '{self.oid}'")
            break

def order_get(pool):
    while True:
        try:
            # 修改SQL查询，解决GROUP BY错误
            order = pool.get_order(f"SELECT * FROM qingka_wangke_order WHERE cid IN (5393,5385,5383,5500) AND (status = '待处理' or status = '补刷中') ORDER BY oid")
            if order:
                for i in order:
                    try:
                        oid = i['oid']
                        cid = i['cid']
                        school = i['school']
                        username = i['user']
                        password = i['pass']
                        kcid = i['kcid']
                        threading.Thread(target=Run,args=(oid,cid, school, username, password, kcid)).start()
                        time.sleep(5)
                    except Exception as e:
                        logger.error(f"创建订单处理线程失败: {str(e)}")
                        traceback.print_exc()
                        continue
        except Exception as e:
            logger.error(f"获取订单列表失败: {str(e)}")
            traceback.print_exc()
        
        # 无论是否发生异常，都等待60秒后继续循环
        time.sleep(30)

def Run(oid,cid, school, username, password, courseid):
    try:
        d = pool.get_order_dan(f"SELECT * FROM qingka_wangke_order WHERE user = '{username}' AND status  IN('进行中','停止中') AND cid = '{cid}'")
        pool.update_order(
            f"update qingka_wangke_order set status = '等待中' , process = '0%',remarks = '账号中存在进行中课程，等待其执行完毕后执行(请等待)......' where cid = '{cid}' and user = '{username}' and status = '待处理'")

        if d is None:
            session = requests.session()
            session.headers.update({
                "User-Agent": UA().WEB,
                "X-Requested-With": "XMLHttpRequest"
            })
            session = StartSession(session)
            MyXxt = MainXxt(session, school, username, password, courseid,oid,cid,pool)
            l = MyXxt.fanyalogin()
            if l is True:
                logger.success(f"ID:{username},登录成功")
                pool.update_order(
                    f"update qingka_wangke_order set status = '进行中' , process = '0%',remarks = '【登录成功】 - 课件获取中......' where oid = '{oid}'")
                k = MyXxt.kclist()
                if k is True:
                    logger.success(f"ID:{username},课程信息匹配成功")
                    MyXxt.studentcourse()
                    MyXxt.studentstudy()  # 开始进行学习阶段
                else:
                    logger.error(f"ID:{username},课程信息匹配失败")
                    pool.update_order(
                        f"update qingka_wangke_order set status = '异常' , process = '0%',remarks = '课程信息匹配失败' where oid = '{oid}'")
            else:
                logger.error(f"ID:{username},{l}")
                pool.update_order(f"update qingka_wangke_order set status = '异常' , process = '0%',remarks = '{l}' where oid = '{oid}'")
            time.sleep(5)
            pool.update_order(
                f"update qingka_wangke_order set status = '待处理' , process = '0%',remarks = '分配资源中......' where cid = '{cid}' and user = '{username}' and status = '等待中'")
    except Exception as e:
        logger.error(f"ID:{username},执行异常: {str(e)}")
        traceback.print_exc()
        pool.update_order(f"update qingka_wangke_order set status = '异常' , process = '0%',remarks = '执行异常: {str(e)}' where oid = '{oid}'")

if __name__ == '__main__':
    try:
        pool = OrderProcessorsql()
        sql = """
            UPDATE qingka_wangke_order
            SET status = '待处理', process = '', remarks = ''
            WHERE cid IN (5393,5385,5383,5500)
            AND status IN ('进行中', '等待中', '排队中', '停止中','休息中')
            """
        pool.update_order(sql)
        logger.success(f"数据洗涤完成... ...")
        time.sleep(3)
        order_get(pool)
    except Exception as e:
        logger.error(f"主程序异常: {str(e)}")
        traceback.print_exc()
