import json
import re
import time
import traceback
from datetime import datetime

from bs4 import BeautifulSoup
from loguru import logger

def t():
    return int(time.time() * 1000)

class StartProces:
    def __init__(self,session,list_info):
        self.session = session
        self.kcname = list_info[0]['kcname']
        self.clazzid = list_info[0]['clazzid']
        self.cpi = list_info[0]['cpi']
        self.courseid = list_info[0]['courseid']

    def get_xxt_jxz(self):
        try:
            r = self.session.get(f"https://stat2-ans.chaoxing.com/study-data/index", params={
                "courseid": self.courseid,
                "clazzid": self.clazzid,
                "cpi": self.cpi,
                "ut": "s",
                "t": t()
            }).text
            # 使用正则表达式匹配 jobEnc 的值
            res_html = BeautifulSoup(r, 'html.parser')
            try:
                txt = str(res_html.findAll("script")[10])
                self.pEnc2 = re.search(r"var jobEnc = '([^']+)';", txt).group(1)
                self.pEnc = res_html.find("input", {"id": "pEnc"}).get("value")
            except (IndexError, AttributeError) as e:
                logger.error(f"解析jobEnc失败: {str(e)}")
                return '10%', '数据解析失败 - 订单正常进行中......'
                
            r = self.session.get(f"https://stat2-ans.chaoxing.com/stat2/task/s/index", params={
                "courseid": self.courseid,
                "cpi": self.cpi,
                "clazzid": self.clazzid,
                "ut": "s",
                "pEnc": self.pEnc2
            }).text
            res = BeautifulSoup(r, 'html.parser')
            
            try:
                video_lei = re.sub(r"[\t\n\r 　 ]+", "",
                                res.find("div", {"class": "list"}).find("div", {"class": "centerC"}).text.strip())
                video_zong = res.find("div", {"class": "list"}).find("p", {"class": "bottomC fs12"}).text.strip()
            except AttributeError as e:
                logger.error(f"解析视频信息失败: {str(e)}")
                video_lei = "未知"
                video_zong = "未知"
                
            try:
                res_len = self.session.get("https://stat2-ans.chaoxing.com/stat2/study-data/job", params={
                    "clazzid": self.clazzid,
                    "courseid": self.courseid,
                    "cpi": self.cpi,
                    "ut": "s",
                    "pEnc": self.pEnc
                }).json()['data']
                process = f"{res_len['jobPer']}%"
            except (KeyError, ValueError, TypeError) as e:
                logger.error(f"获取进度数据失败: {str(e)}")
                return '10%', '进度获取失败 - 订单正常进行中......'
                
            try:
                cishu = res_html.find("div", {"class": "single-list mt-25"}).find("h2").text
            except AttributeError:
                cishu = "未知"
                
            try:
                res_ce = BeautifulSoup(
                    self.session.get("https://stat2-ans.chaoxing.com/stat2/chapter-exam/s/index", params={
                        "courseid": self.courseid,
                        "cpi": self.cpi,
                        "clazzid": self.clazzid,
                        "ut": "s"
                    }).text, 'html.parser')

                ce_lei = res_ce.find("div", {"class": "user-info"}).find("div", {"class": "leftC"}).get(
                    "aria-label").strip().replace("完成进度;", "")
            except (AttributeError, TypeError) as e:
                logger.error(f"获取测验数据失败: {str(e)}")
                ce_lei = "未知"

            # ce_zong = res_ce.find("div",{"class":"user-info"}).findAll("div",{"class":"time"})[1].text.strip()
            # self.video_liebiao = f"任务:{res_len['job']}/{res_len['publishJobNum']}"
            self.video_jinxing = f"课程任务:{res_len.get('job', '?')}/{res_len.get('publishJobNum', '?')} | 章节测验:{ce_lei}"
            return process, self.video_jinxing
        except Exception as e:
            logger.error(f"进度获取异常: {str(e)}")
            traceback.print_exc()
            return '10%', '进度更新异常 - 订单正常进行中......'
            
    def get_xxt_wc(self):
        try:
            res_exam = self.session.get("https://stat2-ans.chaoxing.com/stat2/exam-stastics/stu-exams", params={
                "clazzid": self.clazzid,
                "courseid": self.courseid,
                "cpi": self.cpi,
                "ut": "s",
                "pEnc": "",
                "page": "1",
                "pageSize": "999",
                "personId": self.cpi
            }).json()['data']
            if res_exam:
                return False
            return True
        except Exception as e:
            logger.error(f"考试数据获取异常: {str(e)}")
            return True  # 出错时默认返回已完成

