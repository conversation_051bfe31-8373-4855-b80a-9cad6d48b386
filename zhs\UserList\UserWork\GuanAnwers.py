import time
from SQL.ShouLu import *
import js2py
import requests
from loguru import logger
def answers(questionid,access_token):
    try:
        paramMap = {}
        encrypted_str = 'bHh4XnFfDYprnOcYwUpPfxFfE2DWlEXbz4AIwEeJ5QvUkNjkGRJcdwSab6PKDibGkMOBLOT9or/nas7MyMrjLzof8VaWOsdTBn/vgDM14M+ubEo3itu051bjGWizlMwtgJ2mEli/uspItbefvVIKkeTc5hzhn5m14uCxxH3ZLHM='
        js_code = open(r"C:\Users\<USER>\Desktop\zhs\官库\zhs.js", 'r', encoding='utf-8').read()
        context = js2py.EvalJs()
        context.execute(js_code)
        result = context.workTest(questionid, encrypted_str)
        paramMap["secretStr"] = result
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Cache-Control": "max-age=0",
            "Connection": "keep-alive",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1",
            'User-Agent': 'android.zhihuishu.coma_zd',
            "sec-ch-ua": "^\\^Not",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "^\\^Windows^^",
            'Authorization': access_token,
        }
        # 发起 POST 请求
        response = requests.post('https://studyservice-api.zhihuishu.com/gateway/t/v1/api/videoquestion/lessonPopupExam',
                             headers=headers, data=paramMap, timeout=5).json()
    except:
        r = requests.post(url='http://8.152.6.35:8989/answer',params={'question':questionid},headers={'Authorization':access_token}).json()
        return r
    return response



def Answer_get(question,Authorization):
    try:
        r = requests.post("https://zhs.yunxue.icu/api/data/search", params={"q": question}).json()
    except:
        r = {"code": 0}
    if r['code'] == 1:
        answerid = str(r['data'][0]['answers']).split("###")
        logger.debug(f"数据库答案:{answerid}")
    else:
        r = answers(question,Authorization)
        answerid = []
        # try:ShouLu_Question(sql).shoulu(r)
        # except:pass
        for i in r["data"][0]["testQuestion"]["questionOptions"]:
            if i['result'] == '1':
                answerid.append(i['id'])
        logger.debug(f"管库答案:{answerid}")
    return answerid
