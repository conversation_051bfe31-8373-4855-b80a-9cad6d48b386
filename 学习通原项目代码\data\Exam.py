import difflib
import json
import random
import time
import parsel
import py_mini_racer
from bs4 import BeautifulSoup
from datetime import datetime
from ddddocr import DdddOcr
from API.Re import strip_title,strip_options
from urllib.parse import urlparse, parse_qs
from API.Questionbank import *
def t():
    return int(time.time() * 1000)
def time_s(target_time_str):
    for date_format in ("%m-%d %H:%M", "%Y-%m-%d %H:%M"):
        try:
            target_time = datetime.strptime(target_time_str, date_format)
            current_time_str = datetime.now().strftime(date_format)
            current_time = datetime.strptime(current_time_str, date_format)
            if current_time > target_time:
                comparison = "大于"
            elif current_time < target_time:
                comparison = "小于"
            else:
                comparison = "等于"
            if comparison == '大于':
                return True
            return False
        except ValueError:
            continue
    return False
def get_q(i2,q):
    a = 0
    b = 0
    qlist = ['(1.0)','(2.0)','(3.0)','（1.0）','（2.0）','（3.0）']
    for i in qlist:
        b += 1
        if i in q:
            a = 1
            break
    if a == 1:
        a = 0
        for i in q[::-1]:
            a += 1
            if b > 3:
                if i == '（':
                    q = q[:-a]
                    break
            else:
                if i == '(':
                    q = q[:-a]
                    break
    try:
        if i2.find("h3", {"class": "mark_name colorDeep"}).find_all("img", src=True):
            q += "".join([img['src'] for img in
                                 i2.find("h3", {"class": "mark_name colorDeep"}).find_all("img",
                                                                                          src=True)
                                 if img.get('src')])
    except:pass
    return q
def get_a(answer):
    order = {'A': 1, 'B': 2, 'C': 3, 'D': 4, 'E': 5, 'F': 6, 'G': 7}
    def custom_sort(x):
        return order.get(x, 0)
    sorted_str = ''.join(sorted(answer, key=custom_sort))
    return sorted_str
class EXAM:
    def __init__(self,session,username,list_info,open=1):
        self.session = session
        self.open = open
        self.username = username
        self.kcname = list_info[0]['kcname']
        self.clazzid = list_info[0]['clazzid']
        self.cpi = list_info[0]['cpi']
        self.courseid = list_info[0]['courseid']

    def get_data(self):
        res = self.session.get(f"https://stat2-ans.chaoxing.com/stat2/exam-stastics/stu-exams?clazzid={self.clazzid}&courseid={self.courseid}&cpi={self.cpi}&ut=s&pEnc=&page=1&pageSize=99&personId=").json()
        a = 0
        for i in res["data"]:
            title = i['titleAll']
            self.examtid = i['wrId']
            self.examid = i["examRelationAnswerId"]
            if i['statusStr'] == '待解答':
                if time_s(i['startTime']):
                    logger.success(f"ID：{self.username},考试名称:{title} - 准备进行答题")
                    html = self.OpenExam()
                    self.Do(html)
                    a = 1
        if a == 1:
            time.sleep(random.randint(10,15))
            return True
        return False

    def Captcha(self):
        with open('Y:\yuanma\学习通\data\Captcha.js', 'r', encoding='utf-8') as fp:
            js_code = fp.read()
        ctx = py_mini_racer.MiniRacer()
        ctx.eval(js_code)
        res = BeautifulSoup(self.session.get(f"https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?courseId={self.courseid}&classId={self.clazzid}&examId={self.examtid}&cpi={self.cpi}").text,'html.parser')
        CaptchaId = res.find("input",{"id":"captchaCaptchaId"}).get("value").strip()
        self.openid = parse_qs(urlparse(res.find("div",{"class":"next_btn_div fr pos_bottom"}).find("a").get("data")).query).get('openc', [None])[0]
        _ = json.loads(self.session.get(
            f"https://captcha.chaoxing.com/captcha/get/conf?callback=cx_captcha_function&captchaId={CaptchaId}&_={t()}",proxies={}).text.replace(
            "cx_captcha_function(", "").replace(")", ""))['t']
        input(_)
        result = ctx.call("GetCaptcha", _, CaptchaId)
        res = json.loads(self.session.get("https://captcha.chaoxing.com/captcha/get/verification/image",params={
            "callback": "cx_captcha_function",
            "captchaId": CaptchaId,
            "type": "slide",
            "version": "1.1.20",
            "captchaKey": result['captchaKey'],
            "token": result['token'],
            "referer": "https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes",
            "iv": result['IV'],
            "_": _
        },proxies={}).text[20:][:-1])
        img1 = self.session.get(res["imageVerificationVo"]["cutoutImage"],proxies={}).content
        img2 = self.session.get(res["imageVerificationVo"]["shadeImage"],proxies={}).content
        x = DdddOcr(show_ad=False).slide_match(img1,img2,simple_target=True)['target'][0]
        resp = json.loads(self.session.get("https://captcha.chaoxing.com/captcha/check/verification/result",params={
            "callback": "cx_captcha_function",
            "captchaId": CaptchaId,
            "type": "slide",
            "token": res['token'],
            "textClickArr": "[{\"x\":"+ str(x) +"}]",
            "coordinate": "[]",
            "runEnv": "10",
            "version": "1.1.20",
            "t": "a",
            "iv": result['IV'],
            "_": _
        },headers={
            "Host": "captcha.chaoxing.com",
            "Referer": "https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes"
        },proxies={}).text[20:][:-1])
        if resp['result']:
            v = json.loads(resp["extraData"])['validate']
            logger.success(f"ID:{self.username},考试滑块距离计算正确:{x} 滑块验证码返回:{v}")
            return v
        logger.debug(f"ID:{self.username},考试滑块距离计算错误 正在准备重新精准计算")
        time.sleep(random.randint(1,2))
        return self.Captcha()
    def GetEnc(self,v):
        res = self.session.get("https://mooc1.chaoxing.com/exam-ans/exam/test/examcheck",params={
            "view": "json",
            "answerId": self.examid,
            "examId": self.examtid,
            "classId": self.clazzid,
            "courseId": self.courseid,
            "cpi": self.cpi,
            "code": "",
            "sdlkey": "",
            "facekey": "",
            "captchavalidate": v,
            "_signcode": ""
        }).json()['enc']
        return res
    def OpenExam(self):
        try:
            v = self.Captcha()
        except:
            traceback.print_exc()
        open_enc = self.GetEnc(v)
        params = {
            "courseId": self.courseid,
            "classId": self.clazzid,
            "tId": self.examtid,
            "id": self.examid,
            "tag": "1",
            "enc": open_enc,
            "cpi": self.cpi,
            "openc": self.openid,
            "newMooc": "true"
        }
        resop = BeautifulSoup(
            self.session.get("https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew", params=params).text, 'html.parser')
        reszheng =self.session.get("https://mooc1.chaoxing.com/exam-ans/mooc2/exam/preview",params={
            "courseId": self.courseid,
            "classId": self.clazzid,
            "start": "0",
            "cpi": self.cpi,
            "examRelationId": self.examtid,
            "examRelationAnswerId": self.examid,
            "newMooc": "true",
            "openc": "",
            "monitorStatus": "0",
            "monitorOp": "-1",
            "remainTimeParam": resop.find("input",{"id":"remainTime"}).get("value"),
            "relationAnswerLastUpdateTime":resop.find("input",{"id":"encLastUpdateTime"}).get("value"),
            "enc": resop.find("input",{"id":"enc"}).get("value")
        }).text
        return reszheng
    def post_cs(self,html):
        txt = BeautifulSoup(html, 'html.parser')
        self.testPaperId = txt.find("input",{"id":"testPaperId"}).get("value")
        self.examCreateUserId = txt.find("input",{"id":"examCreateUserId"}).get("value")
        self.testUserRelationId = txt.find("input",{"id":"testUserRelationId"}).get("value")
        self.encRemainTime = txt.find("input",{"id":"encRemainTime"}).get("value")
        self.encLastUpdateTime = txt.find("input",{"id":"encLastUpdateTime"}).get("value")
        self.enc = txt.find("input",{"id":"enc"}).get("value")
        self.examRelationId = txt.find("input",{"id":"examRelationId"}).get("value")
        self.enterPageTime = txt.find("input",{"id":"enterPageTime"}).get("value")
    def Do(self,html):
        self.post_cs(html)
        for js,i in enumerate(parsel.Selector(html).xpath("//div[@class='whiteDiv']/div")):
            self.s = 'true' if js < len(parsel.Selector(html).xpath("//div[@class='whiteDiv']/div")) - 1 else 'false'
            i2 = BeautifulSoup(i.get(),'html.parser')
            self.qid = i.xpath("@data").get()
            self.qtp = int(i2.find("input",{"name":f"type{self.qid}"}).get("value").strip())
            q = strip_title(i2.find("h3",{"class":"mark_name colorDeep"}).text).replace(strip_title(i2.find("span",{"class":"colorShallow"}).get("aria-label")),"")
            self.question = get_q(i2,q)
            answer,self.qnum = questionbank(self.kcname,self.question)
            self.ans = answer
            if self.qtp == 0 or self.qtp == 1:
                Alist,Xlist = self.Answers(i2,answer)
                # print(Alist,Xlist)
                ans = "".join(list(OrderedDict.fromkeys(get_a(Xlist))))
                self.preview(ans)
            if self.qtp == 3:
                t = ['正确','T','True','true','对','是','yes','√','正确']
                a = 0
                for i in t:
                    if i in answer:
                        a = 1
                        break
                ans = 'true' if a == 1 else 'false'
                self.preview(ans)
            # 不需要每题都等待，只在最后一题提交时等待
            if self.s == 'false':
                time.sleep(0.5)
            else:
                time.sleep(0.05)
    def preview(self,answer):
        ans = f"answer{self.qid}" if self.qtp != 1 else f"answers{self.qid}"
        # if self.s == 'false':
        #     time.sleep(random.randint(600,900))
        res = self.session.post("https://mooc1.chaoxing.com/exam-ans/exam/test/preview-save",data={
            "courseId": self.courseid,
            "testPaperId": self.testPaperId,
            "examCreateUserId": self.examCreateUserId,
            "feedbackEnc": "",
            "testUserRelationId": self.testUserRelationId,
            "classId": self.clazzid,
            "type": "0",
            "remainTime": '',
            "tempSave": self.s,
            "timeOver": "false",
            "encRemainTime": self.encRemainTime,
            "encLastUpdateTime": self.encLastUpdateTime,
            "enc": self.enc,
            "userId": "",
            "cpi": self.cpi,
            "examRelationId": self.examRelationId,
            "enterPageTime": self.enterPageTime,
            "exitdtime": "0",
            "monitorforcesubmit": "0",
            f"type{self.qid}": self.qtp,
            "questionId": self.qid,
            f"typeName{self.qid}": "",
            "start": "0",
            ans: answer
        },headers={"Referer": "https://mooc1.chaoxing.com/exam-ans/mooc2/exam/preview"}).text
        logger.info(f"ID:{self.username},question:{self.question},answer:{self.ans},status:{res}")
    def Answers(self,html,answer,bd=0.95):
        Xlist = list()
        Alist = list()
        if answer is not None:
            for i in html.find("div",{"class":"stem_answer"}).find_all("div",{"class":"clearfix answerBg"}):
                x = i.find("span").get("data")
                a = strip_options(i.find("div").text.strip())
                try:
                    if i.find("div", {"class": "fl answer_p"}).find_all("img"):
                        a += "".join(
                            [img.get('src') for img in i.find("div", {"class": "fl answer_p"}).find_all("img") if
                             img.get('src')])
                except:pass
                for ans in answer.split("###"):
                    if self.qtp == 0:
                        if difflib.SequenceMatcher(None, a, ans).ratio() > bd:
                            Xlist.append(x)
                            Alist.append(a)
                            return Alist, Xlist
                    if self.qtp == 1:
                        if difflib.SequenceMatcher(None, a, ans).ratio() > bd:
                            Xlist.append(x)
                            Alist.append(a)
                            break
            if Alist:
                return Alist, Xlist
            if self.qnum == 1:
                answer, self.qnum = questionbank2(self.question)
                return self.Answers(html, answer, bd=0.9)
            elif self.qnum == 2:
                answer, self.qnum = fujia(self.question)
                return self.Answers(html, answer, bd=0.9)
            elif self.qnum == 3:
                self.qnum = 4
                return self.Answers(html, answer, bd=0.5)
            else:
                return '答案匹配失败',['A']
        return '答案匹配失败',['A']



if __name__ == '__main__':
    session = requests.session()
    session.headers={
        "Cookie": 'k8sexam=**********.916.2715.818175; jrose=0A082F5A1DEC1E723657EBC761388302.mooc-exam-*********-8ntc5; source=""; thirdRegist=0; schoolId=129837; k8s=**********.94.8796.920936; route=9dad54174dd60dc20bb47d99b18d6f40; videojs_id=5447621; writenote=yes; tl=1; uname=""; jrose=441CB4D068A878AF4EA7BD6745B92581.mooc-**********-2x70h; fid=4684; _uid=*********; uf=d9387224d3a6095bf074fd62a2f69d94001acc1591681349f9775deb6b414b0b6c899f46b20fbc97fcd538756ccdc738913b662843f1f4ad6d92e371d7fdf644ef759f2e481f6eb70246270a56330e6c3ad59b143144275bc3a3c76c6a5dc08ad552431173659c60; _d=1741624133098; UID=*********; vc2=B3AB84A9E961A5692BB49FD62CE82520; vc3=Y4%2Ba%2BGbWLsMa9ICqLPISzLTHYOHBuP%2BEeHBwMhWbU81kI%2FPHzmiUy6m9O5Ls5GfycZ7wxT6sA90SIa1dCU431i4LbyatFIVpoR4qoRmoH9rGMni9TXnEpjeoifAwCLuAg65j2PjcjdNz98ceYjTzGVW6hlGsnCfZ9P0fJobke8A%3Dd41b2ec7362a58427192dcbc21d2e356; cx_p_token=1f805c9da301b127b0c1a516763e6fcb; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIzNDE0NjM5MjMiLCJsb2dpblRpbWUiOjE3NDE2MjQxMzMwOTksImV4cCI6MTc0MjIyODkzM30.sm1cQCpxTPB7Lphf1jL63aP0NG7vAZeUmqzZbTAg39g; xxtenc=9a4196df62b27a4872e58dbc77d1c9f8; DSSTASH_LOG=C_38-UN_3791-US_*********-T_1741624133099',
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36 Edg/117.0.2045.36",
        "X-Requested-With": "XMLHttpRequest"
    }
    list_info = [{"courseid":"250856463","clazzid":"117280978","cpi":"405328372","kcname":"中国民间艺术的奇妙之旅"}]
    ex = EXAM(session,'0000',list_info,open=1)
    ex.get_data()
