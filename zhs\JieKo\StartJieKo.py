import traceback

import requests, time

#   查询订单
api_order = f"http://www.haohaoxuexi.online/api/self.zhs.php?act=get_list_zhs"
#   查询进行中
api_status = f"http://www.haohaoxuexi.online/api/self.zhs.php?act=get_list_status"
#   分单
api_update_user = f"http://www.haohaoxuexi.online/api/self.zhs.php?act=update_user"
#   改进度
api_order_status = f"http://www.haohaoxuexi.online/api/self.zhs.php?act=order_status"
#  自动考试
api_order_ks = f"http://www.haohaoxuexi.online/api/self.zhs.php?act=get_list_zhs_jieke"
#   获取待考试订单
api_order_get_kaoshi = f"http://www.haohaoxuexi.online/api/self.zhs.php?act=get_list_zhs_ks"

MAX_POST = 10  # 最大连接数

from loguru import logger


def order_get():
    for i in range(MAX_POST):
        try:
            r = requests.post(api_order, data={"key": "zhs123"}).json()
            return r
        except:
            time.sleep(10)
            continue


def order_ks_get():
    for i in range(MAX_POST):
        try:
            r = requests.post(api_order_get_kaoshi, data={"key": "zhs123"}).json()
            return r
        except:
            time.sleep(10)
            continue

def order_end_get():
    for i in range(MAX_POST):
        try:
            r = requests.post(api_order_ks, data={"key": "zhs123"}).json()
            return r
        except:
            time.sleep(10)
            continue
def order_status(oid, status=None, process=None, remarks=None, examStartTime=None, examEndTime=None,ks_status=None,DayTime=None,endtime=None):
    for i in range(MAX_POST):
        try:
            r = requests.post(api_order_status, data={"key": "zhs123", "oid": oid, "status": status, "process": process
                , "remarks": remarks, "examStartTime": examStartTime, "examEndTime": examEndTime,"ks_status":ks_status,"DayTime":DayTime,"endtime":endtime})
            logger.debug(r.json())
            break
        except:
            time.sleep(10)
            continue


def order_status_user(cid, username, status='待处理',DayTime='2024-08-02'):
    '''
    :status: 待处理 （全部改为待处理）
    :status: 列队中 （全部改为列队中）
    :param cid:
    :param username:
    :param status:
    :return:
    '''
    for i in range(MAX_POST):
        try:
            r = requests.post(api_update_user,
                              data={"key": "zhs123", "cid": cid, "username": username, "status": status,"DayTime":DayTime}).json()
            logger.debug(r)
            break
        except:
            time.sleep(10)
            continue


def post_user_status(username: int, cid: int):
    for i in range(MAX_POST):
        try:
            res = requests.post(api_status, data={"key": "zhs123", "cid": cid, "username": username}).json()
            if res['data']:
                #   有进行中
                return True
            else:
                #   无进行
                return False
        except:
            time.sleep(10)
            continue
