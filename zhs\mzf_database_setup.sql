-- mzf 数据库表结构设置
-- 用于学习通自动化系统

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `mzf` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `mzf`;

-- 1. 订单表（已存在于项目中）
-- 这个表结构来自 qingka_wangke_order.sql
DROP TABLE IF EXISTS `qingka_wangke_order`;
CREATE TABLE `qingka_wangke_order` (
  `oid` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL,
  `cid` int(11) NOT NULL COMMENT '平台ID',
  `hid` int(11) NOT NULL COMMENT '接口ID',
  `yid` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '对接站ID',
  `ptname` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '平台名字',
  `school` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '学校',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '姓名',
  `user` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '账号',
  `pass` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '密码',
  `phone` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '手机号',
  `kcid` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '课程ID',
  `kcname` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '课程名字',
  `courseStartTime` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '课程开始时间',
  `courseEndTime` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '课程结束时间',
  `examStartTime` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '考试开始时间',
  `examEndTime` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '考试结束时间',
  `chapterCount` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '总章数',
  `unfinishedChapterCount` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '剩余章数',
  `cookie` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'cookie',
  `fees` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '扣费',
  `noun` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '对接标识',
  `miaoshua` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '0' COMMENT '0不秒 1秒',
  `addtime` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '添加时间',
  `ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '下单ip',
  `dockstatus` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '0' COMMENT '对接状态 0待 1成  2失 3重复 4取消',
  `loginstatus` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `status` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '待处理',
  `process` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `bsnum` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '0' COMMENT '补刷次数',
  `remarks` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '备注',
  `score` varchar(11) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '分数',
  `doForum` int(11) NULL DEFAULT NULL COMMENT '国开单页 --是否完成讨论 1完成 0不完成',
  `doExam` int(11) NULL DEFAULT NULL COMMENT '国开单页 --是否完成形考 1完成 0不完成',
  `doHomework` int(11) NULL DEFAULT NULL COMMENT '国开单页 --是否完成大作业 1完成 0不完成',
  `doActivities` int(11) NULL DEFAULT NULL COMMENT '国开单页 --是否完成任务点 1完成 0不完成',
  `doHomeworkMode` int(11) NULL DEFAULT NULL COMMENT '国开单页 --大作业提交模式 1直接提交 0保存草稿，学生可基于我们保存的草稿添加自己要的东西再自己点提交',
  `doNightLearn` int(11) NULL DEFAULT NULL COMMENT '国开单页 --夜间是否学习 1学习 0不学习',
  `tag` int(11) NULL DEFAULT NULL COMMENT '国开单页 --下单标签',
  `isNewGk` int(11) NULL DEFAULT NULL COMMENT '国开单页 --是否是单页下单',
  PRIMARY KEY (`oid`) USING BTREE,
  INDEX `hid`(`hid`, `user`, `kcname`, `addtime`) USING BTREE,
  INDEX `hid_2`(`hid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci ROW_FORMAT = Dynamic;

-- 2. 智慧树题库表
-- 根据代码中的使用情况推断的表结构
DROP TABLE IF EXISTS `zhihuishutiku`;
CREATE TABLE `zhihuishutiku` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `qtype` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '题目类型',
  `qid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '题目ID',
  `question` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '题目内容',
  `answerid` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '答案ID（多个用#分隔）',
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '答案内容（多个用#分隔）',
  `answerlist` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所有选项（用#分隔）',
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始数据JSON',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_qid` (`qid`) USING BTREE,
  INDEX `idx_qtype` (`qtype`) USING BTREE,
  INDEX `idx_question` (`question`(255)) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '智慧树题库表' ROW_FORMAT = Dynamic;
