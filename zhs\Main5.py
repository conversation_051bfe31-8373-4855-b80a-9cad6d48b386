import datetime
import multiprocessing
import random
import threading
import time
import traceback

from SQL.SQL_SHOU import *

import requests
from loguru import logger
from MainSession.Session import StartSession
from UserLogin.Login import Status_Login
from UserList.UserKclist import StatusKclist
from UserList.VideoList import StartVideo
from UserList.UserWork.Work import StartWork
from UserList.Meet.MainMeet import StartMeet
from UserFAN.MainFAN import StartFAN
from UserList.HuDong.MainHD import StartHU
from UserList.Progress import ProGress
from JieKo.StartJieKo import *
from loguru import logger

miaoshua = 2  # 慢刷自动转秒 限制天数


def ks_time_pipei(data: str, status=1):
    specified_time = datetime.datetime.strptime(data, '%Y-%m-%d %H:%M:%S')
    # 获取当前时间
    current_time = datetime.datetime.now()
    if status == 2:
        start = specified_time - current_time
        start = int(start.days)
        # 对比当前时间和指定时间
        if start < 0:
            return '结课'
        elif start <= miaoshua:
            return True
        else:
            return False
    else:
        # 对比当前时间和指定时间
        if current_time < specified_time:
            return False
        elif current_time > specified_time:
            return True
        else:
            return True


def video(session, courseid, recruitid, classid, schoolid, userid, uuid, cid):
    return StartVideo(session, courseid, recruitid, classid, schoolid, userid, uuid, cid).app_video_list()


def work(session, courseid, recruitid, classid, schoolid, userid, uuid, progres, oid, examtime, examstatus, status):
    return StartWork(session, courseid, recruitid, classid, schoolid, userid, uuid, progres, oid, examtime, examstatus,
                     status).work_dohomework()


def meet(session, courseid, recruitid, classid, schoolid, userid, uuid):
    StartMeet(session, courseid, recruitid, classid, schoolid, userid, uuid).start()


def fanzhuan(session, courseid, uuid, userid):
    StartFAN(session, courseid, uuid, userid).start()


def hudong(session, courseid, recruitid, classid, schoolid, userid, uuid):
    StartHU(session, courseid, recruitid, classid, schoolid, userid, uuid).start()


def Login_Main1(oid, cid, school, username, password, kcname, kcid, examtime, examstatus, sql=None,addtime=None):
    session = requests.session()
    addtime = str(addtime)
    session = StartSession(session)
    login = Status_Login(session, school, username, password)
    UserList = []
    status, msg = login.login_main(UserList)
    if status:  # 登陆成功
        try:
            logger.success(msg.split(",")[0])
            kclist = StatusKclist(session)
            list = kclist.app_get_kclist(kcid)
            kclist.get_app_kclist_fanzhuan(kcid, list, UserList[0]['uuid'])
            if list:
                courseid = kcid
                try:
                    endTime = list[0]['endTime']
                except:
                    endTime = None
                recruitid = list[0]['recruitId']
                classid = list[0]['classId']
                userid = UserList[0]['userid']
                uuid = UserList[0]['uuid']
                schoolid = UserList[0]['schoolId']
                if cid == 1971:  # 翻转课
                    order_status(oid=oid, status='进行中', process='0%')
                    fanzhuan(session, courseid, uuid, userid)  # 翻转课
                    order_status(oid=oid, status='已完成', process='100%', remarks='翻转课完成（急速秒单）')
                elif cid == 1984:  # 互动分
                    order_status(oid=oid, status='进行中', process='0%')
                    hudong(session, courseid, recruitid, classid, schoolid, userid, uuid)  # 互动分
                    order_status(oid=oid, status='已完成', process='100%',
                                 remarks='已完成70次回答+70次提问+70次点赞（隔天凌晨更新分数）')
                elif cid == 1982:  # 单考试
                    progres = ProGress(session, courseid, recruitid, classid, schoolid, userid, uuid)
                    work_status = work(session, courseid, recruitid, classid, schoolid, userid, uuid, progres, oid,
                                       examtime, examstatus, 1)  # 答题 1考试未开始 2考试完成
                    if work_status == 1:
                        process, xiguan, remarks, daytime = progres.getProgress()
                        order_status(oid=oid, status='待考试', process=process, remarks=f"考试未开始:{remarks}")
                    else:
                        time.sleep(30)
                        process, xiguan, remarks, daytime = progres.getProgress()
                        order_status(oid=oid, status='已考完', process=process, remarks=f"考试完成:{remarks}")
                elif cid == 1983:  # 补习惯分
                    progres = ProGress(session, courseid, recruitid, classid, schoolid, userid, uuid)
                    VideoStatus = video(session, courseid, recruitid, classid, schoolid, userid, uuid, cid)  # 视频
                    process, xiguan, remarks, daytime = progres.getProgress()
                    if VideoStatus is True:
                        if xiguan == 0:
                            status = '已完成'
                        else:
                            status = '平时分'
                        order_status(oid=oid, status=status, process=process,
                                     remarks=f"习惯分中还需规律学习{xiguan}天:{remarks}", DayTime=daytime,
                                     endtime=endTime)
                else:
                    progres = ProGress(session, courseid, recruitid, classid, schoolid, userid, uuid)
                    process, xiguan, remarks, daytime = progres.getProgress()
                    order_status(oid=oid, status='进行中', process=process, remarks=remarks)
                    if cid == 1981:
                        status = 3  # 补习惯分
                    else:
                        status = 2
                    if cid == 1970:
                        if process == '100%' and xiguan != 0:
                            cid = 1980

                    work_status = work(session, courseid, recruitid, classid, schoolid, userid, uuid, progres, oid,
                                       examtime, examstatus, status)  # 答题 1单考试 2考试+章节测试 3不考试
                    VideoStatus = video(session, courseid, recruitid, classid, schoolid, userid, uuid, cid)  # 视频
                    meet(session, courseid, recruitid, classid, schoolid, userid, uuid)  # 见面课
                    if datetime.datetime.strptime(addtime,
                                                  "%Y-%m-%d %H:%M:%S").date() >= datetime.datetime.now().date():
                        hudong(session, courseid, recruitid, classid, schoolid, userid, uuid)  # 互动分
                    time.sleep(30)
                    process, xiguan, remarks, daytime = progres.getProgress()
                    if VideoStatus is True:
                        if cid == 1980 and xiguan == 0:
                            if work_status == 1:
                                order_status(oid=oid, status='待考试', process=process, remarks=f"等待开考:{remarks}",
                                             DayTime=daytime)
                            elif work_status == 2:
                                process, xiguan, remarks, daytime = progres.getProgress()
                                order_status(oid=oid, status='已完成', process=process, remarks=f"考试未提交:{remarks}",
                                             DayTime=daytime)
                            else:
                                order_status(oid=oid, status='已完成', process=process, remarks=f"已全部完成{remarks}",
                                             DayTime=daytime)
                        else:
                            order_status(oid=oid, status='平时分', process=process,
                                         remarks=f"习惯分中还需规律学习{xiguan}天:{remarks}", DayTime=daytime,
                                         endtime=endTime)
                    else:
                        if xiguan == 0:
                            if work_status == 1:
                                order_status(oid=oid, status='待考试', process=process, remarks=f"等待开考:{remarks}",
                                             DayTime=daytime)
                            elif work_status == 2:
                                process, xiguan, remarks, daytime = progres.getProgress()
                                order_status(oid=oid, status='已完成', process=process, remarks=f"考试未提交:{remarks}",
                                             DayTime=daytime)
                            else:
                                order_status(oid=oid, status='已完成', process=process, remarks=f"已全部完成{remarks}",
                                             DayTime=daytime)
                        else:
                            order_status(oid=oid, status='平时分', process=process,
                                         remarks=f"习惯分中还需规律学习{xiguan}天:{remarks}", DayTime=daytime,
                                         endtime=endTime)
                order_status_user(cid, username)
                logger.success(f"账号完成")
            else:
                logger.error('课程id匹配失败')
                order_status(oid=oid, status='异常', process='0%', remarks=f"课程ID匹配失败 请用云学查课下单")
                order_status_user(cid, username)
                logger.success(f"账号完成")
        except:
            traceback.print_exc()
            time.sleep(30)
            return Login_Main1(oid, cid, school, username, password, kcname, kcid, examtime, examstatus, sql,addtime)
    else:
        logger.error(msg)
        order_status(oid=oid, status='异常', process='0%', remarks=msg)

def Main_Test(i):
    print(i)
    oid = i['oid']
    cid = int(i['cid'])
    school = i['school']
    username = i['user']
    password = i['pass']
    addtime = i['addtime']
    kcname = i['kcname']
    kcid = i['kcid']
    dati_time = 1
    kaoshi_status = 1
    if kcid is None or kcid == '':
        order_status(oid, '异常', '100%', '课程id为空')
    else:
        try:
            data = int(kcid)
            order_status_user(cid, username, status='列队中')
            if post_user_status(username, cid):
                pass
            else:
                Login_Main1(oid, cid, school, username, password, kcname, kcid, dati_time,kaoshi_status,addtime=addtime)
        except:
            logger.debug(f"课程id是偷得")
            order_status(oid,status='异常',process='0%',remarks='偷查课也不偷个一样的【课程id错误】')


def start_jxz():
    current_time = datetime.datetime.now()
    DayTime = current_time.strftime('%Y-%m-%d')
    order_status_user(1, 1, status='启动', DayTime=DayTime)
    logger.success(f"全部进行中账号重启成功")


def KAOSHI_MAIN():
    process = multiprocessing.Process(target=start_exam, args=())
    process.start()
    process.join()


def END_MAIN():
    process = multiprocessing.Process(target=start_end, args=())
    process.start()
    process.join()


def start_exam():
    while True:
        time.sleep(3600)
        r = order_ks_get()
        if r['data']:
            for i in r['data']:
                from datetime import datetime
                if datetime.strptime("2024-09-01 00:00:00", "%Y-%m-%d %H:%M:%S") <= datetime.strptime(i['addtime'],"%Y-%m-%d %H:%M:%S") < datetime.strptime("2024-10-20 00:00:00", "%Y-%m-%d %H:%M:%S"):


                    oid = i['oid']
                    addtime = i['addtime']
                    school = i['school']
                    username = i['user']
                    password = i['pass']
                    kcname = i['kcname']
                    kcid = i['kcid']
                    examStartTime = i['examStartTime']
                    dati_time = i['exam_time']
                    kaoshi_status = i['is_submit_exam']
                    if ks_time_pipei(examStartTime):
                        threading.Thread(target=Login_Main1,
                                         args=(
                                         oid, 1541, school, username, password, kcname, kcid, dati_time, kaoshi_status,
                                         addtime)).start()
                        time.sleep(20)
                    else:
                        pass


def start_end():
    while True:
        time.sleep(3600)
        r = order_end_get()
        if r['data']:
            for i in r['data']:
                from datetime import datetime
                if datetime.strptime("2024-09-01 00:00:00", "%Y-%m-%d %H:%M:%S") <= datetime.strptime(i['addtime'],"%Y-%m-%d %H:%M:%S") < datetime.strptime("2024-10-20 00:00:00", "%Y-%m-%d %H:%M:%S"):

                    oid = i['oid']
                    school = i['school']
                    username = i['user']
                    password = i['pass']
                    kcname = i['kcname']
                    addtime = i['addtime']
                    kcid = i['kcid']
                    examStartTime = i['courseEndTime']
                    dati_time = i['exam_time']
                    kaoshi_status = i['is_submit_exam']
                    status = ks_time_pipei(examStartTime, status=2)
                    if status:
                        if status == '结课':
                            order_status(oid, status='已结课')
                        else:
                            threading.Thread(target=Login_Main1,
                                             args=(oid, 1539, school, username, password, kcname, kcid, dati_time,
                                                   kaoshi_status,addtime)).start()
                            time.sleep(5)
                    else:
                        pass


def MEIRI():
    process = multiprocessing.Process(target=MEISHI_SHANG)
    process.start()
    process.join()


def MEISHI_SHANG():
    while True:
        current_time = datetime.datetime.now().time()
        if current_time >= datetime.time(1, 0) and current_time < datetime.time(2, 0):
            logger.success(f"每日上号进程已启动")
            current_time = datetime.datetime.now()
            DayTime = current_time.strftime('%Y-%m-%d')
            order_status_user(1, 1, '每日上号', DayTime=DayTime)
            time.sleep(43200)
            continue
        logger.success(f"每日上号进程监控中")
        time.sleep(2400)


import multiprocessing
import time
from concurrent.futures import ThreadPoolExecutor
import requests

from loguru import logger
def worker_process(worker_id, order_queue, n_threads):
    with ThreadPoolExecutor(max_workers=n_threads) as executor:
        while True:
            try:
                # 获取订单
                order = order_queue.get()
                # 如果 order 是 None，表示接收到终止信号
                if order is None:
                    break
                # 使用线程池执行登录操作
                executor.submit(Main_Test,order)
            except Exception as e:
                logger.error(f"工作进程 {worker_id} 遇到错误: {e}")
class OrderProcessor:
    def __init__(self):
        self.n_workers = 12
        self.n_threads = 200
        self.check_interval = 20
        self.workers_tim = 1
        self.worker_queues = [multiprocessing.Queue() for _ in range(self.n_workers)]
        self.workers = []

    def fetch_and_mark_orders(self):
        try:
            data = order_get()['data']
            return data
        except Exception as e:
            logger.error(f"获取和标记订单时出错: {e}")

    def start_workers(self):
        # 为每个工作进程创建一个新的Queue对象
        self.worker_queues = [multiprocessing.Queue() for _ in range(self.n_workers)]
        for i in range(self.n_workers):
            p = multiprocessing.Process(
                target=worker_process,
                args=(i, self.worker_queues[i], self.n_threads),
                name=f"Worker-{i}"
            )
            p.start()
            self.workers.append(p)
            time.sleep(self.workers_tim)

    def distribute_orders(self):
        try:
            while True:
                pending_orders = self.fetch_and_mark_orders()
                if pending_orders:
                    for idx, order in enumerate(pending_orders):
                        oid = order['oid']
                        # worker_queue = self.worker_queues[idx % self.n_workers]
                        worker_queue = self.worker_queues[int(oid) % self.n_workers]
                        # worker_queue = min(self.worker_queues, key=lambda q: q.qsize())
                        worker_queue.put(order)
                        time.sleep(5)

                time.sleep(self.check_interval)
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在关闭...")
        finally:
            for q in self.worker_queues:
                q.put(None)
            for p in self.workers:
                p.join()
            logger.info("所有工作进程已关闭。")

    def run(self):
        self.start_workers()
        self.distribute_orders()

if __name__ == '__main__':

    OrderProcessor().run()

