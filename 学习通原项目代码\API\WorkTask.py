import traceback

from API import Re
from collections import OrderedDict

from loguru import logger
from bs4 import BeautifulSoup
from API.Questionbank import *
import difflib
import json
import re
import time
def tiankong_quchong(text):
    items = text.split('###')
    unique_items = list(OrderedDict.fromkeys(item.strip() for item in items))
    result = '###'.join(unique_items)

    return result
class StaratWorkTaks:
    def __init__(self,session,couserid,classid,cpi,listid,jobid,kcname,username):
        self.session = session
        self.couserid = couserid
        self.kcname = kcname
        self.classid = classid
        self.cpi = cpi
        self.listid = listid
        self.jobid = jobid
        self.username = username
        self.userid = self.session.Uid()

    def Html_Wkrk(self,html):
        self.html = BeautifulSoup(html,'html.parser')
        if '已批阅' in self.html.find("title").text.strip() or '待批阅' in self.html.find("title").text.strip():
            logger.success(f"ID:{self.username},","本章节已提交")
        else:
            try:
                self.workAnswerId = self.html.find("input", {"id": "workAnswerId"}).get("value")
                self.totalQuestionNum = self.html.find("input", {"id": "totalQuestionNum"}).get("value")
                self.old = self.html.find("input", {"id": "oldWorkId"}).get("value")
                self.workRelationId = self.html.find("input", {"id": "workRelationId"}).get("value")
                self.enc_work = self.html.find("input", {"id": "enc_work"}).get("value")
                self.params = dict()
                answerwqbid = list()
                for quest in self.html.findAll("div", {"class": "Py-mian1 singleQuesId"}):
                    self.qid = quest.get("data")
                    question = quest.find("div", {"class": "Py-m1-title fs16 fontLabel"}).text.strip()
                    self.qtp = int(quest.find("input", {"name": f"answertype{self.qid}"}).get("value"))
                    question = re.sub(r'^\d+\.', "", re.sub(r"【.*?】|【.*】|\[.*?\]|\[.*]", "", question))
                    # re.sub(r"[0-9\]", "",
                    if quest.find("div", {"class": "Py-m1-title fs16 fontLabel"}).find_all("img", src=True):
                        question += "".join([img['src'] for img in
                                             quest.find("div", {"class": "Py-m1-title fs16 fontLabel"}).find_all("img",
                                                                                                                 src=True)
                                             if img.get('src')])
                    self.question = Re.strip_title(question)
                    logger.debug(f"{self.qtp}-{self.question}")
                    answer, self.qnum = questionbank(self.kcname, self.question)
                    if self.qtp == 0 or self.qtp == 1:
                        if answer is not None:
                            try:
                                answer, htmlanswer = self.Xuan(quest, answer)
                                answers = "".join(list(OrderedDict.fromkeys(self.__paixun(answer))))
                            except Exception as e:
                                logger.error(f"ID:{self.username},选择题处理错误: {str(e)}")
                                answers = 'A'
                                htmlanswer = '答案处理错误'
                        else:
                            answers = 'A'
                            htmlanswer = '无答案'
                        logger.success(f"answer:{answers}-{htmlanswer}")
                    elif self.qtp == 2:
                        tk = False
                        if answer is not None:
                            # answer = tiankong_quchong(answer)
                            lenanswer = len(quest.findAll("div", {"input": "blankInp2 answerInput escapeInput"}))
                            if len(answer.split("###")) == lenanswer:
                                tk = True
                        else:
                            answer = list()
                        logger.info(f"answer:{answer}")
                        for a, i in enumerate(quest.findAll("input", {"class": "blankInp2 answerInput escapeInput"})):
                            tkid = i.get('id').replace("answer", "")
                            if tk is True:
                                tk_answers = answer.split("###")[a]
                            else:
                                try:
                                    tk_answers = answer.split("###")[a]
                                except:
                                    tk_answers = ''
                            self.params[f'answerEditor{tkid}'] = f'<p>{tk_answers}</p>'
                        tiankongsize = quest.find("input", {"name": f"tiankongsize{self.qid}"}).get('value')
                        self.params[f'tiankongsize{self.qid}'] = tiankongsize
                    elif self.qtp == 3:
                        if answer is not None:
                            if 'true' in answer or '对' in answer or '正确' in answer:
                                answers = 'true'
                            else:
                                answers = 'false'
                        else:
                            answers = 'true'
                        logger.info(f"answer:{answers}")
                    elif self.qtp == 4 or self.qtp == 5 or self.qtp == 6 or self.qtp == 7 or self.qtp == 8 or self.qtp == 10 or self.qtp == 14:
                        if answer is not None:
                            answers = f'<p>{answer}</p>'
                        else:
                            answers = '<p> </p>'
                    elif self.qtp == 19:
                        token = [i.get("value") for i in quest.findAll("input", {"name": "readCompreHension-childId"})]
                        token_type = [i.get("value") for i in
                                      quest.findAll("input", {"name": "readCompreHension-childType"})]
                        tl_list = {}
                        for a, id in enumerate(token):
                            if answer:
                                try:
                                    tl_answer = answer.split("###")[a]
                                except:
                                    tl_answer = ''
                            else:
                                tl_answer = ''
                            logger.info(f"ID:{self.username},听力{id} - ans:{tl_answer}")
                            self.params[f'answer{id}'] = tl_answer
                            tl_list[id] = {"type": token_type[a], "answer": tl_answer}
                        self.params['readCompreHension-childId'] = '&'.join(token)
                        self.params['readCompreHension-childType'] = '&'.join(token_type)
                        data = json.dumps([tl_list])
                        self.params[f'answer{self.qid}'] = data
                        self.params[f'answertype{self.qid}'] = 19
                    else:
                        answers = 'A'
                        # print(quest)
                    if self.qtp != 2 and self.qtp != 19:
                        self.params[f'answer{self.qid}'] = answers
                    self.params[f'answertype{self.qid}'] = self.qtp
                    answerwqbid.append(self.qid)
                    time.sleep(0.5)
                self.params['answerwqbid'] = ','.join(answerwqbid) + ','
                self.PostDo()
                time.sleep(0.5)
            except:
                traceback.print_exc()
                logger.info(f"ID:{self.username},章节老师未设置内容")
    def Xuan(self,quest,answers,bidui=0.95):
        AnswerList = []
        Answer = []
        if answers is not None:
            if quest.findAll("li",{"class":"clearfix cur more-choose-item"}):
                for ans in quest.findAll("li",{"class":"clearfix cur more-choose-item"}):
                    xuan = ans.find("em", {"class": "choose-opt"}).get("id-param").strip()
                    answer = ans.find("div", {"class": "choose-desc"}).text.strip()
                    if ans.find("div", {"class": "choose-desc"}).find_all("img"):
                        answer += "".join(
                            [img.get('src') for img in ans.find("div", {"class": "choose-desc"}).find_all("img") if
                             img.get('src')])
                    for i in answers.split("###"):
                        if self.qtp == 0:
                            if bidui != 0.65:
                                if answer == i:
                                    AnswerList.append(xuan)
                                    Answer.append(answer)
                                    return AnswerList, Answer
                            else:
                                if difflib.SequenceMatcher(None, answer, i).ratio() > bidui:
                                    AnswerList.append(xuan)
                                    Answer.append(answer)
                                    return AnswerList, Answer
                        elif self.qtp == 1:
                            if bidui != 0.65:
                                if answer == i:
                                    AnswerList.append(xuan)
                                    Answer.append(answer)
                            else:
                                if difflib.SequenceMatcher(None, answer, i).ratio() > bidui:
                                    AnswerList.append(xuan)
                                    Answer.append(answer)
            for ans in quest.findAll("li", {"class": "clearfix more-choose-item"}):
                xuan = ans.find("em", {"class": "choose-opt"}).get("id-param").strip()
                answer = Re.strip_title(ans.find("div", {"class": "choose-desc"}).text.strip())

                if ans.find("div", {"class": "choose-desc"}).find_all("img"):
                    answer += "".join([img.get('src') for img in ans.find("div", {"class": "choose-desc"}).find_all("img") if img.get('src')])
                for i in answers.split("###"):
                    if self.qtp == 0:
                        if bidui != 0.65:
                            if answer == i:
                                AnswerList.append(xuan)
                                Answer.append(answer)
                                return AnswerList, Answer
                        else:
                            if difflib.SequenceMatcher(None, answer, i).ratio() > bidui:
                                AnswerList.append(xuan)
                                Answer.append(answer)
                                return AnswerList, Answer
                    elif self.qtp == 1:
                        if bidui != 0.65:
                            if answer == i:
                                AnswerList.append(xuan)
                                Answer.append(answer)
                                break
                        else:
                            if difflib.SequenceMatcher(None, answer, i).ratio() > bidui:
                                AnswerList.append(xuan)
                                Answer.append(answer)
                                break
            if AnswerList:
                return AnswerList,Answer
            if self.qnum == 1:
                answers,self.qnum = questionbank2(self.question)
                return self.Xuan(quest,answers,bidui=0.9)
            elif self.qnum == 2:
                answers, self.qnum = fujia(self.question)
                return self.Xuan(quest, answers,bidui=0.9)
            elif self.qnum == 3:
                self.qnum = 4
                return self.Xuan(quest, answers,bidui=0.7)
            else:
                return ['A'], '答案匹配失败'
        else:
            # 如果answers为None，返回默认值
            return ['A'], '无答案'

    def __paixun(self, daan):
        order = {'A': 1, 'B': 2, 'C': 3, 'D': 4, 'E': 5, 'F': 6, 'G': 7}
        def custom_sort(x):
            return order.get(x, 0)
        sorted_str = ''.join(sorted(daan, key=custom_sort))
        return sorted_str

    def PostDo(self):
        data = {
            "pyFlag": "",
            "courseId": self.couserid,
            "classId": self.classid,
            "api": "1",
            "mooc": "0",
            "workAnswerId": self.workAnswerId,
            "totalQuestionNum": self.totalQuestionNum,
            "fullScore": "100.0",
            "knowledgeid": self.listid,
            "oldSchoolId": "",
            "old": self.old,
            "jobid": self.jobid,
            "workRelationId": self.workRelationId,
            "enc_work": self.enc_work,
            "isphone": "true",
            "userId": self.userid,
            "workTimesEnc": "",
            ** self.params
        }
        r = self.session.post("https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNew",data=data)
        if '''提交失败，参数异常''' in r.text:
            logger.error(f"ID:{self.username},{data}")
        else:
            logger.success(f"ID:{self.username},{r.text}")
