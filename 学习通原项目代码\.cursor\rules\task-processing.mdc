---
description: 
globs: 
alwaysApply: false
---
# 学习通自动化系统 - 任务处理规则

## 任务类型处理

### 视频任务处理
- **初始化**: 通过`VideoStart`类处理视频任务，位于`API/VideoTask.py`
- **播放模拟**: 模拟真实播放过程，每秒增加播放时间并上报进度
- **完成条件**: 根据服务器返回的`isPassed`状态判断任务是否完成
- **特殊处理**: 支持音频(`Audio`)和视频(`Video`)两种媒体类型

### 作业任务处理
- **初始化**: 通过`StaratWorkTaks`类处理作业任务，位于`API/WorkTask.py`
- **答案获取**: 通过`questionbank`函数从题库获取答案
- **题型识别**: 支持单选(0)、多选(1)、填空(2)、判断(3)、简答(4/5/6/7/8/10/14)等多种题型
- **答案匹配**: 使用文本相似度算法匹配选项，支持多级匹配策略
- **提交方式**: 通过`PostDo`方法提交作业答案

### 考试任务处理
- **初始化**: 通过`EXAM`类处理考试任务，位于`data/Exam.py`
- **验证码处理**: 使用滑块验证码识别技术解决考试验证码
- **答题流程**: 逐题获取答案并提交，最后一题提交完成整个考试
- **特殊处理**: 考试开始时间检查，只处理已开始的考试

### 讨论任务处理
- **初始化**: 通过`Topic`方法处理讨论任务，位于`API/TaskDo.py`
- **内容获取**: 随机复制已有讨论内容进行回复
- **提交方式**: 通过POST请求提交讨论内容

## 任务执行流程

### 课程任务流程
1. **获取课程列表**: 通过`kclist`方法获取用户所有课程
2. **匹配目标课程**: 根据订单中的课程ID匹配对应课程
3. **获取章节列表**: 通过`studentcourse`方法获取课程章节
4. **章节任务处理**: 通过`studentstudy`方法处理每个章节的任务点
5. **完成状态更新**: 根据任务完成情况更新订单状态

### 任务点处理流程
1. **任务类型识别**: 根据`attachment`的`type`字段识别任务类型
2. **调用对应处理器**: 根据任务类型调用相应的处理方法
3. **执行任务**: 完成任务并获取结果
4. **等待间隔**: 任务完成后等待一定时间再处理下一个任务点

## 错误处理策略

### 网络错误处理
- **重试机制**: 网络请求失败后会进行重试
- **等待策略**: 错误后等待一定时间再重试，避免频繁请求
- **日志记录**: 详细记录错误信息，便于排查问题

### 答案匹配失败处理
- **多级匹配**: 使用不同的匹配阈值尝试匹配答案
- **多源查询**: 尝试从不同题库获取答案
- **默认答案**: 无法匹配时使用默认答案(如'A')

### 任务异常处理
- **任务跳过**: 某个任务点失败不影响其他任务点执行
- **状态更新**: 任务异常时更新订单状态和备注
- **异常恢复**: 系统重启后能恢复异常订单的处理

