import hashlib
import re

# 移除各种空白字符
def remove_blank(text):
    return re.sub(r"[\t\n\r 　 ]+", "", text)


# 修剪课程
def strip_course(course):
    course = remove_blank(course)
    course = re.sub(r"\([^()]*\)|（[^（）]*）|【[^【】]*】|\[[^\[\]]*]|\{[^{}]*}", '', course)
    course = re.sub(r'[^\u4e00-\u9fa5]', '', course)
    course = re.sub(r'副本|重修|春|夏|秋|冬|季|第|学|期|年|级|版|班级|一|二|三|四|五|六|七|八|九|十|上|下', '', course)
    if not course:
        course = "其他"
    return course


# 中文字符转英文字符
def transform_char(text):
    text = text.replace("，", ",")
    text = text.replace("：", ":")
    text = text.replace("！", "!")
    text = text.replace("？", "?")
    text = text.replace("（", "(")
    text = text.replace("）", ")")
    text = text.replace("【", "[")
    text = text.replace("】", "]")
    text = text.replace("“", "\"")
    text = text.replace("”", "\"")
    text = text.replace("‘", "\'")
    text = text.replace("’", "\'")
    return text


# 修剪题目
def strip_title(text):
    text = re.sub(r'<.*?>', '', text)  # 去除HTML标签
    text = remove_blank(text)  # 移除空白字符
    text = transform_char(text)  # 中文转英文
    text = text.replace("\'", "")  # 去除英文单引号
    text = re.sub(r"\(\d+\.\d+分\)", "", text)
    return text

# 修剪题目
def strip_title2(text):
    text = re.sub(r"\(\d+\.\d+分\)", "", text)
    text = re.sub(r'<.*?>', '', text)  # 去除HTML标签
    text = remove_blank(text)  # 移除空白字符
    text = transform_char(text)  # 中文转英文
    text = text.replace("\'", "")  # 去除英文单引号

    return text

# 修剪选项
def strip_options(text):
    text = transform_char(text)  # 中文转英文
    text = remove_blank(text)  # 移除空白字符
    text = text.replace("\'", "")  # 去除英文单引号
    return text

