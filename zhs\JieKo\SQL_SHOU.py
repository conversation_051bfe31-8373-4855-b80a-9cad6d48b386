import time
from dbutils.pooled_db import PooledDB
import pymysql
from loguru import logger
#   提单子
class start_main_sql:
    def __init__(self):
        logger.success('\t\t\t --- --- --- 题库服务器 正在启动')
    def lianjie(self):
        db_config = {
            'host': "localhost",
            'port': 3306,
            'user': 'root',
            'password': 'dhjdhjdhj',
            'db': '智慧树',
            'charset': 'utf8mb4',
            'autocommit': True  # 自动提交事务
        }
        # db_config = {
        #     'host': "**************",
        #     'port': 3306,
        #     'user': 'xxt',
        #     'password': 'xxt',
        #     'db': 'xxt',
        #     'charset': 'utf8mb4',
        #     'autocommit': True  # 自动提交事务
        # }
        for i in range(10):
            try:
                self.pool = PooledDB(pymysql, mincached=1, maxcached=500, maxconnections=100, **db_config)
                logger.success('\t\t\t --- --- --- 题库服务器 启动成功')
                break
            except:
                time.sleep(10)
                continue

    def sql_cha(self,sql):
        try:
            connection = self.pool.connection()
            cursor = connection.cursor()
            cursor.execute(sql)
            data = cursor.fetchall()
            return data
        finally:
            # 释放连接到连接池
            cursor.close()
            connection.close()

    def sql_chadan(self,sql):
        try:
            connection = self.pool.connection()
            cursor = connection.cursor()
            cursor.execute(sql)
            data = cursor.fetchone()
            return data
        finally:
            # 释放连接到连接池
            cursor.close()
            connection.close()
    def sql_question(self,question,questions):
        try:
            connection = self.pool.connection()
            cursor = connection.cursor()
            query = "select * from gaofen_tiku where question = %s and questions = %s"
            cursor.execute(query, (question, questions))
            data = cursor.fetchone()
            return data
        finally:
            # 释放连接到连接池
            cursor.close()
            connection.close()
    def sql_update(self,sql):
        try:
            connection = self.pool.connection()
            cursor = connection.cursor()
            cursor.execute(sql)
            # 释放连接到连接池
            cursor.close()
            connection.close()
        except:
            logger.success('\t\t\t --- --- --- 进度暂时不更新')

    #   写入
    def sql_xiru(self,sql):
        try:
            connection = self.pool.connection()
            cursor = connection.cursor()
            cursor.execute(sql)
            connection.commit()

        finally:
            # 释放连接到连接池
            cursor.close()

    def sql_xiru_question(self,question_type,qid,question,answerid,answers,data):
        try:
            connection = self.pool.connection()
            cursor = connection.cursor()
            sql = "INSERT INTO zhihuishutiku (qtype,qid,question,answerid,answers,data) VALUES(%s, %s, %s, %s,%s,%s)"
            # 要插入的数据
            data = (question_type,qid,question,answerid,answers,data)
            # 执行 JieKo 插入语句
            cursor.execute(sql, data)
            connection.commit()
        finally:
            # 释放连接到连接池
            cursor.close()


